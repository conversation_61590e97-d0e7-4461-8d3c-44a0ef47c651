package br.com.alice.ehr.routes

import br.com.alice.appointment.event.AppointmentCompletedEvent
import br.com.alice.appointment.event.AppointmentCreatedEvent
import br.com.alice.appointment.event.DraftAppointmentDeletedEvent
import br.com.alice.channel.event.AssistanceScreeningChatCreatedEvent
import br.com.alice.common.extensions.inject
import br.com.alice.common.kafka.internals.ConsumerJob
import br.com.alice.ehr.consumers.*
import br.com.alice.ehr.event.EmergencyExecutedEvent
import br.com.alice.ehr.event.PregnancyUpsertEvent
import br.com.alice.ehr.event.TestRequestExecutedEvent
import br.com.alice.eventinder.events.HealthEventExecutedEvent
import br.com.alice.exec.indicator.events.GuiaWithProceduresUpsertedEvent
import br.com.alice.exec.indicator.events.TotvsGuiaUpsertedEvent
import br.com.alice.healthcondition.event.CaseRecordCreatedEvent
import br.com.alice.healthcondition.event.PersonCaseUpdatedEvent
import br.com.alice.healthlogic.event.AutomaticTaskEngineResultEvent
import br.com.alice.healthlogic.event.ClinicalOutcomeRecordCreatedEvent
import br.com.alice.healthplan.events.HealthPlanTaskCreatedExternallyEvent
import br.com.alice.healthplan.events.HealthPlanTaskGroupPublishedEvent
import br.com.alice.healthplan.events.HealthPlanTaskUpsertedEvent
import br.com.alice.membership.model.events.HealthDeclarationFinishedEvent
import br.com.alice.person.model.events.MemberActivatedEvent
import br.com.alice.person.model.events.MemberCancelledEvent
import br.com.alice.questionnaire.event.HealthFormCompletedEvent
import br.com.alice.questionnaire.event.HealthFormConfidenceQuestionAnswerEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCancelledEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCreatedEvent
import br.com.alice.secondary.attention.events.CounterReferralCreatedEvent
import br.com.alice.staff.event.HealthProfessionalCreatedEvent
import br.com.alice.staff.event.HealthProfessionalUpdatedEvent
import br.com.alice.testresult.events.DischargeSummaryUpsertEvent
import br.com.alice.wanda.event.PersonHealthEventUpdatedEvent

fun ConsumerJob.Configuration.kafkaRoutes() {
    val healthFormConsumer by inject<HealthFormConsumer>()
    consume("handle-pre-immersion-event", HealthFormCompletedEvent.name, healthFormConsumer::handlePreImmersionEvent)

    val healthDeclarationFinishedConsumer by inject<HealthDeclarationFinishedConsumer>()
    consume(
        "create-diseases-background",
        HealthDeclarationFinishedEvent.name,
        healthDeclarationFinishedConsumer::createDiseasesBackground
    )
    consume(
        "create-surgeries-background",
        HealthDeclarationFinishedEvent.name,
        healthDeclarationFinishedConsumer::createSurgeriesBackground
    )
    consume(
        "update-measurements",
        HealthDeclarationFinishedEvent.name,
        healthDeclarationFinishedConsumer::updateMeasurements
    )

    val memberConsumer by inject<MemberConsumer>()
    consume("delete-person-clinical-account", MemberCancelledEvent.name, memberConsumer::deletePersonClinicalAccount)
    consume(
        "create-person-internal-reference",
        MemberActivatedEvent.name,
        memberConsumer::createPersonInternalReference
    )

    val appointmentConsumer by inject<AppointmentConsumer>()
    consume(
        "add-staff-on-person-clinical-account",
        AppointmentCreatedEvent.name,
        appointmentConsumer::addStaffOnPersonClinicalAccount
    )
    consume(
        "register-counter-referral",
        AppointmentCompletedEvent.name,
        appointmentConsumer::addCounterReferralByAppointment
    )
    consume(
        "register-counter-referral-no-show",
        DraftAppointmentDeletedEvent.name,
        appointmentConsumer::addCounterReferralByAppointmentDeleted
    )
    consume(
        handlerName = "appointment-consolidated-rewards",
        topicName = AppointmentCompletedEvent.name,
        handler = appointmentConsumer::processAppointment,
    )

    val appointmentScheduleConsumer by inject<AppointmentScheduleConsumer>()
    consume(
        "disassociate-person-from-healthcare-team",
        AppointmentScheduleCancelledEvent.name,
        appointmentScheduleConsumer::disassociatePersonFromHealthcareTeam
    )
    consume(
        "associate-suggestion-specialist-to-health-plan-task",
        AppointmentScheduleCreatedEvent.name,
        appointmentScheduleConsumer::associateSuggestionSpecialistToHealthPlanTask
    )

    val autoCompleteTaskConsumer by inject<AutoCompleteTaskConsumer>()

    consume(
        "auto-complete-test-request-task",
        TestRequestExecutedEvent.name,
        autoCompleteTaskConsumer::handleTestRequest
    )
    consume(
        "auto-complete-ps-task",
        EmergencyExecutedEvent.name,
        autoCompleteTaskConsumer::handlePs
    )

    val testRequestExecutedConsumer by inject<TestRequestExecutedConsumer>()

    consume(
        "produce-executed-test-request-task",
        HealthEventExecutedEvent.name,
        testRequestExecutedConsumer::handleTestRequestByEventinder
    )

    val emergencyExecutedConsumer by inject<EmergencyExecutedConsumer>()

    consume(
        "produce-executed-ps-task",
        HealthEventExecutedEvent.name,
        emergencyExecutedConsumer::handlePsByEventinder
    )

    consume(
        "auto-complete-schedule-created-task",
        AppointmentScheduleCreatedEvent.name,
        autoCompleteTaskConsumer::handleScheduleCreatedEvent
    )
    consume(
        "auto-complete-schedule-canceled-task",
        AppointmentScheduleCancelledEvent.name,
        autoCompleteTaskConsumer::handleScheduleCanceledEvent
    )

    consume(
        "auto-complete-task-from-appointment",
        AppointmentCompletedEvent.name,
        autoCompleteTaskConsumer::handleAppointmentCompletedEvent
    )

    consume(
        "auto-complete-task-from-person-health-event",
        PersonHealthEventUpdatedEvent.name,
        autoCompleteTaskConsumer::handlePersonHealthEventUpdate
    )

    consume(
        "auto-complete-task-from-referral",
        CounterReferralCreatedEvent.name,
        autoCompleteTaskConsumer::handleReferralEvent
    )

    val healthLogicTransformConsumer by inject<HealthLogicTransformConsumer>()
    consume(
        "health-logic-transform-health-form",
        HealthFormCompletedEvent.name,
        healthLogicTransformConsumer::handleHealthForm
    )

    val healthLogicResultConsumer by inject<HealthLogicResultConsumer>()
    consume(
        "health-logic-result-event",
        AutomaticTaskEngineResultEvent.name,
        healthLogicResultConsumer::handleHealthLogicResult
    )

    val referralUpsertedConsumer by inject<ReferralUpsertedConsumer>()
    consume(
        "referral-created-event",
        HealthPlanTaskUpsertedEvent.name,
        referralUpsertedConsumer::assignMultiStaff
    )

    val clinicalOutcomeRecordCreatedConsumer by inject<ClinicalOutcomeRecordCreatedConsumer>()
    consume(
        "clinical-outcome-record-created-event",
        ClinicalOutcomeRecordCreatedEvent.name,
        clinicalOutcomeRecordCreatedConsumer::createHealthMeasurement
    )
    consume(
        "clinical-outcome-record-created-event-to-upsert-assistance-summary",
        ClinicalOutcomeRecordCreatedEvent.name,
        clinicalOutcomeRecordCreatedConsumer::upsertInAssistanceSummary
    )

    val memedStaffConsumer by inject<MemedStaffConsumer>()
    consume(
        "health-professional-created-event",
        HealthProfessionalCreatedEvent.name,
        memedStaffConsumer::healthProfessionalCreated,
    )
    consume(
        "health-professional-updated-event",
        HealthProfessionalUpdatedEvent.name,
        memedStaffConsumer::healthProfessionalUpdated
    )

    val draftAppointmentFinishedConsumer by inject<DraftAppointmentFinishedConsumer>()
    consume(
        handlerName = "draft-appointment-finished-ehr",
        topicName = AppointmentCompletedEvent.name,
        handler = draftAppointmentFinishedConsumer::consume
    )

    val draftAppointmentDeletedConsumer by inject<DraftAppointmentDeletedConsumer>()
    consume(
        handlerName = "draft-appointment-deleted-ehr",
        topicName = DraftAppointmentDeletedEvent.name,
        handler = draftAppointmentDeletedConsumer::consume,
    )

    val tertiaryIntentionTouchPointCreationConsumer by inject<TertiaryIntentionTouchPointCreationConsumer>()
    consume(
        handlerName = "auto-creation-tertiary-touch-point-ehr",
        topicName = TotvsGuiaUpsertedEvent.name,
        handler = tertiaryIntentionTouchPointCreationConsumer::generateTouchPointByTotvsGuia
    )

    val dischargeSummaryUpsertConsumer by inject<DischargeSummaryUpsertConsumer>()
    consume(
        handlerName = "match-discharge-summary-with-tertiary",
        topicName = DischargeSummaryUpsertEvent.name,
        handler = dischargeSummaryUpsertConsumer::processMatch
    )

    val pregnancyConsumer by inject<PregnancyConsumer>()
    consume(
        handlerName = "pregnancy-upsert-in-assistance-summary",
        topicName = PregnancyUpsertEvent.name,
        handler = pregnancyConsumer::handleUpsert
    )

    val personCaseUpdatedConsumer by inject<PersonCaseUpdatedConsumer>()
    consume(
        handlerName = "person-case-updated-ehr",
        topicName = PersonCaseUpdatedEvent.name,
        handler = personCaseUpdatedConsumer::onUpdate,
    )

    val assistanceScreeningChatCreatedConsumer by inject<AssistanceScreeningChatCreatedConsumer>()
    consume(
        handlerName = "assistance-screening-chat-created-ehr",
        topicName = AssistanceScreeningChatCreatedEvent.name,
        handler = assistanceScreeningChatCreatedConsumer::onCreated,
    )

    val tertiaryIntentionNotificationConsumer by inject<TertiaryIntentionNotificationConsumer>()
    consume(
        handlerName = "register-notification-by-guia",
        topicName = GuiaWithProceduresUpsertedEvent.name,
        handler = tertiaryIntentionNotificationConsumer::generateNotificationByGuia,
    )
    consume(
        handlerName = "register-notification-by-counter-referral",
        topicName = CounterReferralCreatedEvent.name,
        handler = tertiaryIntentionNotificationConsumer::generateNotificationByCounterReferral,
    )

    val counterReferralConsumer by inject<CounterReferralConsumer>()
    consume(
        handlerName = "counter-referral-consolidated-rewards",
        topicName = CounterReferralCreatedEvent.name,
        handler = counterReferralConsumer::processCounterReferral,
    )

    val healthFormConfidenceQuestionAnswerConsumer by inject<HealthFormConfidenceQuestionAnswerConsumer>()
    consume(
        handlerName = "fill-consolidated-rewards-answer",
        topicName = HealthFormConfidenceQuestionAnswerEvent.name,
        handler = healthFormConfidenceQuestionAnswerConsumer::processHealthFormConfidenceQuestionAnswer,
    )

    val ehrStateConsumer by inject<EhrStateConsumer>()
    consume(
        "update-ehr-state-timeline-by-appointment-completed",
        AppointmentCompletedEvent.name,
        ehrStateConsumer::updateByAppointmentCompleted
    )
    consume(
        "update-ehr-state-health-plan-by-health-plan-task-group-published",
        HealthPlanTaskGroupPublishedEvent.name,
        ehrStateConsumer::updateByHealthPlanTaskGroupPublished
    )
    consume(
        "update-ehr-state-health-plan-by-health-plan-task-created-externally",
        HealthPlanTaskCreatedExternallyEvent.name,
        ehrStateConsumer::updateByHealthPlanTaskCreatedExternally
    )
    consume(
        "update-ehr-state-demand-by-case-record-created",
        CaseRecordCreatedEvent.name,
        ehrStateConsumer::updateDemandByCaseRecordCreated
    )

    consume(
        "update-ehr-state-appointment-by-appointment-discarded",
        DraftAppointmentDeletedEvent.name,
        ehrStateConsumer::updateByAppointmentDiscarded
    )
}
